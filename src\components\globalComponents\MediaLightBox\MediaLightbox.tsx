import { useRef } from 'react';
import Lightbox from 'yet-another-react-lightbox';
import Thumbnails from 'yet-another-react-lightbox/plugins/thumbnails';
import Video from 'yet-another-react-lightbox/plugins/video';
import 'yet-another-react-lightbox/styles.css';
import 'yet-another-react-lightbox/plugins/thumbnails.css';
import './lightbox-custom.css';
import { DEFAULT_MEDIA_LIGHTBOX_CONFIG, mergeMediaLightboxConfig, type MediaLightboxGlobalConfig } from './MediaLightboxConfig';

export interface MediaSlide {
  src: string;
  alt: string;
  type?: 'image' | 'video';
  poster?: string;
  thumbnail?: string;
  width?: number;
  height?: number;
  sources?: Array<{
    src: string;
    type: string;
  }>;
}

export interface MediaLightboxProps {
  /** Whether the lightbox is open */
  open: boolean;
  /** Function to close the lightbox */
  onClose: () => void;
  /** Array of media slides to display */
  slides: MediaSlide[];
  /** Current slide index */
  index: number;
  /** Function called when slide changes */
  onSlideChange?: (index: number) => void;
  /** Use a predefined global configuration */
  configType?: 'default' | 'compact' | 'gallery';
  /** Custom thumbnail configuration */
  thumbnailConfig?: {
    position?: 'top' | 'bottom' | 'start' | 'end';
    width?: number;
    height?: number;
    border?: number;
    borderRadius?: number;
    padding?: number;
    gap?: number;
    showToggle?: boolean;
  };
  /** Custom video configuration */
  videoConfig?: {
    controls?: boolean;
    playsInline?: boolean;
    preload?: 'none' | 'metadata' | 'auto';
  };
  /** Custom carousel configuration */
  carouselConfig?: {
    finite?: boolean;
    preload?: number;
    spacing?: `${number}px` | `${number}%` | number;
  };
  /** Custom animation configuration */
  animationConfig?: {
    fade?: number;
    swipe?: number;
    easing?: {
      fade?: string;
      swipe?: string;
    };
  };
  /** Custom controller configuration */
  controllerConfig?: {
    closeOnBackdropClick?: boolean;
    closeOnPullDown?: boolean;
    closeOnPullUp?: boolean;
  };
  /** Custom styles override */
  customStyles?: {
    container?: React.CSSProperties;
    thumbnailsContainer?: React.CSSProperties;
  };
}

/**
 * MediaLightbox - A reusable lightbox component for displaying images and videos
 * 
 * Features:
 * - Support for both images and videos
 * - Thumbnail navigation with customizable position and styling
 * - Blurred background with glassmorphism effect
 * - Smooth animations and transitions
 * - Fully configurable through props
 * - Accessibility support
 * - Mobile responsive
 */
const MediaLightbox = ({
  open,
  onClose,
  slides,
  index,
  onSlideChange,
  configType = 'default',
  thumbnailConfig = {},
  videoConfig = {},
  carouselConfig = {},
  animationConfig = {},
  controllerConfig = {},
  customStyles = {}
}: MediaLightboxProps) => {
  const thumbnailsRef = useRef(null);
  const currentVideoRef = useRef<HTMLVideoElement | null>(null);

  // Helper function to detect if a URL is a video
  const isVideoUrl = (url: string): boolean => {
    const videoExtensions = ['.mp4', '.webm', '.ogg', '.mov', '.avi', '.mkv', '.m4v'];
    const videoHosts = ['gtv-videos-bucket', 'youtube.com', 'vimeo.com', 'sample-videos.com'];

    return videoExtensions.some(ext => url.toLowerCase().includes(ext.toLowerCase())) ||
           videoHosts.some(host => url.toLowerCase().includes(host));
  };

  // Get global configuration based on configType
  const globalConfig = (() => {
    let baseConfig = DEFAULT_MEDIA_LIGHTBOX_CONFIG;

    if (configType === 'compact') {
      baseConfig = mergeMediaLightboxConfig(baseConfig, {
        thumbnailConfig: {
          position: 'bottom',
          width: 80,
          height: 60,
          border: 2,
          borderRadius: 8,
          padding: 4,
          gap: 12,
          showToggle: false,
        },
        customStyles: {
          container: {
            backgroundColor: 'rgba(255, 255, 255, 0.12)',
            backdropFilter: 'blur(15px) saturate(160%)',
          },
          thumbnailsContainer: {
            backgroundColor: 'rgba(255, 255, 255, 0.08)',
            backdropFilter: 'blur(12px) saturate(140%)',
          },
        },
      });
    } else if (configType === 'gallery') {
      baseConfig = mergeMediaLightboxConfig(baseConfig, {
        thumbnailConfig: {
          position: 'bottom',
          width: 120,
          height: 80,
          border: 3,
          borderRadius: 16,
          padding: 8,
          gap: 20,
          showToggle: true,
        },
        carouselConfig: {
          finite: false,
          preload: 3,
          spacing: '24px' as const,
        },
        animationConfig: {
          fade: 300,
          swipe: 500,
          easing: {
            fade: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
            swipe: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
          },
        },
      });
    }

    return baseConfig;
  })();

  // Process slides to ensure proper format for lightbox with video thumbnails
  const processedSlides = slides.map((slide) => {
    const isVideo = slide.type === 'video' || isVideoUrl(slide.src);

    if (isVideo) {
      return {
        type: 'video' as const,
        src: slide.src,
        sources: slide.sources || [
          {
            src: slide.src,
            type: slide.src.toLowerCase().includes('.webm') ? 'video/webm' :
                  slide.src.toLowerCase().includes('.ogg') ? 'video/ogg' : 'video/mp4',
          },
        ],
        alt: slide.alt,
        poster: '', // Remove poster to prevent image fallback
        // Use video file directly as thumbnail - this will render actual video in thumbnail
        thumbnail: slide.src,
        width: slide.width || 1920,
        height: slide.height || 1080,
      };
    }

    return {
      src: slide.src,
      alt: slide.alt,
      thumbnail: slide.thumbnail || slide.src,
      width: slide.width || 1920,
      height: slide.height || 1080,
    };
  });

  // Merge global config with user overrides
  const defaultThumbnailConfig = {
    ...globalConfig.thumbnailConfig,
    imageFit: 'cover' as const,
    vignette: true,
    ...thumbnailConfig
  };

  const defaultVideoConfig = {
    ...globalConfig.videoConfig,
    ...videoConfig
  };

  const defaultCarouselConfig = {
    ...globalConfig.carouselConfig,
    ...carouselConfig
  };

  const defaultAnimationConfig = {
    ...globalConfig.animationConfig,
    easing: {
      ...globalConfig.animationConfig.easing,
      ...animationConfig?.easing
    },
    ...animationConfig
  };

  const defaultControllerConfig = {
    ...globalConfig.controllerConfig,
    ...controllerConfig
  };

  const defaultStyles = {
    container: {
      ...globalConfig.customStyles.container,
      ...customStyles.container
    },
    thumbnailsContainer: {
      ...globalConfig.customStyles.thumbnailsContainer,
      ...customStyles.thumbnailsContainer
    }
  };

  return (
    <Lightbox
      open={open}
      close={onClose}
      slides={processedSlides}
      index={index}
      plugins={[Thumbnails, Video]}
      thumbnails={{
        ref: thumbnailsRef,
        ...defaultThumbnailConfig
      }}
      video={defaultVideoConfig}
      carousel={defaultCarouselConfig}
      animation={defaultAnimationConfig}
      controller={defaultControllerConfig}
      styles={defaultStyles}
      render={{
        slide: ({ slide }) => {
          // Custom slide renderer for videos with auto-play
          if (slide.type === 'video' && 'sources' in slide && slide.sources?.[0]?.src) {
            const videoSrc = slide.sources[0].src;
            return (
              <video
                ref={(el) => {
                  currentVideoRef.current = el;
                }}
                src={videoSrc}
                style={{
                  maxWidth: '100%',
                  maxHeight: '100%',
                  width: 'auto',
                  height: 'auto',
                  background: 'transparent'
                }}
            
                autoPlay
                muted
                loop
                onLoadedData={(e) => {
                  // Ensure video plays when loaded and store reference
                  const video = e.target as HTMLVideoElement;
                  currentVideoRef.current = video;
                  video.play().catch(() => {
                    console.log('Video autoplay was prevented by browser policy');
                  });
                }}
                onCanPlay={(e) => {
                  // Additional trigger when video can play
                  const video = e.target as HTMLVideoElement;
                  video.play().catch(() => {
                    console.log('Video autoplay was prevented by browser policy');
                  });
                }}
              />
            );
          }
          // Return null for non-video slides to use default rendering
          return null;
        },
        thumbnail: ({ slide, rect }) => {
          // Custom thumbnail renderer for videos
          if (slide.type === 'video' && 'sources' in slide && slide.sources?.[0]?.src) {
            const videoSrc = slide.sources[0].src;
            return (
              <div style={{
                width: rect.width,
                height: rect.height,
                position: 'relative',
                overflow: 'hidden',
                borderRadius: '4px'
              }}>
                <video
                  src={videoSrc}
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover',
                    background: 'transparent'
                  }}
                  muted
                  loop
                  playsInline
                  preload="metadata"
                  onLoadedMetadata={(e) => {
                    const video = e.target as HTMLVideoElement;
                    // Seek to 10% of video duration for thumbnail
                    if (video.duration > 1) {
                      video.currentTime = video.duration * 0.1;
                    }
                  }}
                  onMouseEnter={(e) => {
                    // Play video on hover
                    const video = e.target as HTMLVideoElement;
                    video.play().catch(() => {
                      // Ignore play errors (autoplay restrictions)
                    });
                  }}
                  onMouseLeave={(e) => {
                    // Pause video when not hovering
                    const video = e.target as HTMLVideoElement;
                    video.pause();
                  }}
                />
                <div style={{
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  background: 'rgba(0, 0, 0, 0.2)',
                  color: 'white',
                  borderRadius: '50%',
                  width: '24px',
                  height: '24px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '12px',
                  pointerEvents: 'none',
                  zIndex: 10
                }}>
                  ▶
                </div>
              </div>
            );
          }
          // Return null for non-video slides to use default thumbnail
          return null;
        }
      }}
      on={{
        view: ({ index }) => {
          onSlideChange?.(index);

          // Auto-play video when slide becomes active and pause others
          setTimeout(() => {
            // Pause all videos first (except thumbnail videos)
            const allVideos = document.querySelectorAll('.yarl__container video') as NodeListOf<HTMLVideoElement>;
            allVideos.forEach(video => {
              if (!video.closest('.yarl__thumbnails')) { // Don't pause thumbnail videos
                video.pause();
              }
            });

            // Play current video if it's a video slide
            const currentSlide = processedSlides[index];
            if (currentSlide?.type === 'video') {
              // First try: use the ref if available
              let activeVideoElement: HTMLVideoElement | null = currentVideoRef.current;

              // Fallback: try multiple selectors to find the active video
              if (!activeVideoElement || activeVideoElement.closest('.yarl__thumbnails')) {
                // Look for video in active slide
                activeVideoElement = document.querySelector('.yarl__slide--current video') as HTMLVideoElement;

                // Look for video in any visible slide
                if (!activeVideoElement) {
                  activeVideoElement = document.querySelector('.yarl__slide[data-yarl-slide-type="video"] video') as HTMLVideoElement;
                }

                // Look for any video in the main slide area (not thumbnails)
                if (!activeVideoElement) {
                  const slideVideos = document.querySelectorAll('.yarl__slide video') as NodeListOf<HTMLVideoElement>;
                  for (let i = 0; i < slideVideos.length; i++) {
                    const video = slideVideos[i];
                    if (!video.closest('.yarl__thumbnails')) {
                      activeVideoElement = video;
                      break;
                    }
                  }
                }
              }

              if (activeVideoElement && !activeVideoElement.closest('.yarl__thumbnails')) {
                currentVideoRef.current = activeVideoElement; // Update ref
                activeVideoElement.currentTime = 0; // Reset to beginning
                activeVideoElement.play().catch(() => {
                  // Ignore autoplay restrictions
                  console.log('Video autoplay was prevented by browser policy');
                });
              }
            }
          }, 100); // Reduced delay for faster response
        },
        entering: () => {
          // When lightbox opens, auto-play video if current slide is video
          setTimeout(() => {
            const currentSlide = processedSlides[index || 0];
            if (currentSlide?.type === 'video') {
              // Use the same robust video finding logic
              let videoElement: HTMLVideoElement | null = null;

              videoElement = document.querySelector('.yarl__slide--current video') as HTMLVideoElement;
              if (!videoElement) {
                videoElement = document.querySelector('.yarl__slide[data-yarl-slide-type="video"] video') as HTMLVideoElement;
              }
              if (!videoElement) {
                const slideVideos = document.querySelectorAll('.yarl__slide video') as NodeListOf<HTMLVideoElement>;
                for (let i = 0; i < slideVideos.length; i++) {
                  const video = slideVideos[i];
                  if (!video.closest('.yarl__thumbnails')) {
                    videoElement = video;
                    break;
                  }
                }
              }

              if (videoElement) {
                videoElement.currentTime = 0;
                videoElement.play().catch(() => {
                  console.log('Video autoplay was prevented by browser policy');
                });
              }
            }
          }, 500); // Longer delay for initial load
        },
        exiting: () => {
          // Pause all videos when lightbox closes
          const allVideos = document.querySelectorAll('.yarl__container video') as NodeListOf<HTMLVideoElement>;
          allVideos.forEach(video => {
            video.pause();
          });
          currentVideoRef.current = null; // Clear ref
        },
        click: ({ index: clickedIndex }) => {
          // Handle thumbnail clicks for videos
          const clickedSlide = processedSlides[clickedIndex];
          if (clickedSlide?.type === 'video') {
            // Small delay to allow slide transition
            setTimeout(() => {
              // Find and play the video for the clicked slide
              let videoElement: HTMLVideoElement | null = null;

              videoElement = document.querySelector('.yarl__slide--current video') as HTMLVideoElement;
              if (!videoElement) {
                videoElement = document.querySelector('.yarl__slide[data-yarl-slide-type="video"] video') as HTMLVideoElement;
              }

              if (videoElement && !videoElement.closest('.yarl__thumbnails')) {
                currentVideoRef.current = videoElement;
                videoElement.currentTime = 0;
                videoElement.play().catch(() => {
                  console.log('Video autoplay was prevented by browser policy');
                });
              }
            }, 200);
          }
        }
      }}
    />
  );
};

export default MediaLightbox;
