import { MediaSlide } from '.';

/**
 * Utility functions for MediaLightbox component
 */

/**
 * Helper function to detect if a URL is a video
 */
export const isVideoUrl = (url: string): boolean => {
  const videoExtensions = ['.mp4', '.webm', '.ogg', '.mov', '.avi', '.mkv', '.m4v'];
  const videoHosts = ['gtv-videos-bucket', 'youtube.com', 'vimeo.com', 'sample-videos.com'];

  return videoExtensions.some(ext => url.toLowerCase().includes(ext.toLowerCase())) ||
         videoHosts.some(host => url.toLowerCase().includes(host));
};

/**
 * Helper function to create slide object for lightbox
 */
export const createSlide = (src: string, alt: string, type?: 'image' | 'video'): MediaSlide => {
  const isVideo = type === 'video' || isVideoUrl(src);
  
  if (isVideo) {
    return {
      type: 'video',
      src,
      sources: [
        {
          src,
          type: src.toLowerCase().includes('.webm') ? 'video/webm' :
                src.toLowerCase().includes('.ogg') ? 'video/ogg' : 'video/mp4',
        },
      ],
      alt,
      poster: src.replace(/\.(mp4|webm|ogg|mov|avi|mkv|m4v)$/i, '.jpg'),
      thumbnail: src,
      width: 1920,
      height: 1080,
    };
  }
  
  return {
    src,
    alt,
    width: 1920,
    height: 1080,
  };
};

/**
 * Helper function to create slides array from recommendation media
 */
export const createSlidesFromRecommendation = (media: {
  large: string;
  small: string[];
}): MediaSlide[] => {
  return [
    createSlide(media.large, 'Large Image'),
    ...media.small.map((src, index) =>
      createSlide(src, `Small Image ${index + 1}`)
    ),
  ];
};

/**
 * Helper function to create slides from a simple array of URLs
 */
export const createSlidesFromUrls = (urls: string[], altPrefix = 'Image'): MediaSlide[] => {
  return urls.map((url, index) => createSlide(url, `${altPrefix} ${index + 1}`));
};

/**
 * Helper function to create slides with custom alt texts
 */
export const createSlidesFromUrlsWithAlts = (
  items: Array<{ url: string; alt: string; type?: 'image' | 'video' }>
): MediaSlide[] => {
  return items.map(item => createSlide(item.url, item.alt, item.type));
};
